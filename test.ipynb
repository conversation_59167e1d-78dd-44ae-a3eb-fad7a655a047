%pip install matplotlib
import matplotlib.pyplot as plt

current_x = 0
x = 1
current_y = 0
best_x = 0
best_y = 0
best_result = float('inf')

x_values = []
result_values = []

def best_initial_f(x, y):
    return ((x-1)**2 + (2-y)**2)


for i in range(25):
    # Action: Increment x by 0.1 if above threshold; else decrement it.
    new_x = max(0, x + (0.1 if current_x > 1 else -0.1))

    # Check constraints after taking action.
    if not (0 <= new_x <= 10):
        raise ValueError("New X out of bounds!")

    # Update current state
    current_x = new_x
    x = new_x # Update x here
    current_x = round(current_x, 1) # Round to 1 decimal place

    # Calculate result based on updated x
    result_value = ((current_x-1)**2 + (2-current_y)**2)

    print(f"Current X: {current_x}, Result Value: {result_value}")
    
    if result_value < best_result:
        best_result, best_x, best_y = result_value, current_x, current_y
    
    # Update current_y (example: increment by 0.2)
    current_y += 0.2

    x_values.append(current_x)
    result_values.append(result_value)

if not best_result < best_initial_f(x,current_y):
    raise ValueError("Failed to find better solution!")

# Create plots
plt.figure(figsize=(12, 6))

# Plot current_x vs. iteration number
plt.subplot(1, 2, 1)
plt.plot(range(25), x_values)
plt.xlabel("Iteration")
plt.ylabel("Current X")
plt.title("Current X vs. Iteration")

# Plot result_value vs. iteration number
plt.subplot(1, 2, 2)
plt.plot(range(25), result_values)
plt.xlabel("Iteration")
plt.ylabel("Result Value")
plt.title("Result Value vs. Iteration")

plt.tight_layout()
plt.show()

