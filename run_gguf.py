#!/usr/bin/env python3
"""
Simple script to run a GGUF model using llama-cpp-python
"""

from llama_cpp import Llama
import sys

def main():
    # Initialize the model
    print("Loading GGUF model...")
    try:
        llm = Llama(
            model_path="unsloth.Q4_K_M.gguf",
            n_ctx=2048,  # Context window
            n_threads=4,  # Number of CPU threads
            verbose=False
        )
        print("Model loaded successfully!")
        
        # Interactive chat loop
        print("\n" + "="*50)
        print("GGUF Model Chat Interface")
        print("Type 'quit' or 'exit' to stop")
        print("="*50 + "\n")
        
        while True:
            try:
                user_input = input("You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("Goodbye!")
                    break
                
                if not user_input:
                    continue
                
                print("Assistant: ", end="", flush=True)
                
                # Generate response
                response = llm(
                    user_input,
                    max_tokens=256,
                    temperature=0.7,
                    top_p=0.9,
                    echo=False,
                    stop=["User:", "You:"]
                )
                
                print(response['choices'][0]['text'].strip())
                print()
                
            except KeyboardInterrupt:
                print("\n\nGoodbye!")
                break
            except Exception as e:
                print(f"Error generating response: {e}")
                
    except Exception as e:
        print(f"Error loading model: {e}")
        print("Make sure the GGUF file 'unsloth.Q4_K_M.gguf' exists in the current directory.")
        sys.exit(1)

if __name__ == "__main__":
    main()
